
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Professional Legal Color Palette */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    /* Deep Navy Blue - Professional and Trustworthy */
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;

    /* Light Professional Gray */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Warm Gold Accent */
    --accent: 25 95% 53%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 83% 53%;

    --radius: 0.75rem;

    /* Professional Sidebar Colors */
    --sidebar-background: 222 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 221 83% 53%;

    /* Professional Legal Theme Variables */
    --legal-navy: 221 83% 53%;
    --legal-navy-dark: 221 83% 43%;
    --legal-navy-light: 221 83% 63%;
    --legal-burgundy: 0 59% 41%;
    --legal-gold: 25 95% 53%;
    --legal-gold-light: 25 95% 63%;
    --legal-gray: 215 16% 47%;
    --legal-gray-light: 215 16% 65%;
    --legal-gray-dark: 215 16% 25%;
  }

  .dark {
    /* Professional Dark Mode for Legal Theme */
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 217.2 32.6% 17.5%;
    --card-foreground: 210 40% 98%;

    --popover: 217.2 32.6% 17.5%;
    --popover-foreground: 210 40% 98%;

    --primary: 221 83% 63%;
    --primary-foreground: 222 84% 4.9%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 25 95% 63%;
    --accent-foreground: 222 84% 4.9%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 221 83% 63%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 221 83% 63%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 221 83% 63%;

    /* Dark Mode Legal Theme Variables */
    --legal-navy: 221 83% 63%;
    --legal-navy-dark: 221 83% 53%;
    --legal-navy-light: 221 83% 73%;
    --legal-burgundy: 0 59% 51%;
    --legal-gold: 25 95% 63%;
    --legal-gold-light: 25 95% 73%;
    --legal-gray: 215 16% 65%;
    --legal-gray-light: 215 16% 75%;
    --legal-gray-dark: 215 16% 35%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* Custom Aesthetic Scrollbar - Black & White Theme */
  html {
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: #333333 #f8f9fa; /* Firefox - thumb and track colors */
  }

  /* Custom scrollbar for WebKit browsers (Chrome, Safari, Edge) */
  *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  /* Hide scrollbar for homepage and sidebar */
  .homepage-container::-webkit-scrollbar,
  .homepage-container *::-webkit-scrollbar,
  .sidebar-container::-webkit-scrollbar,
  .sidebar-container *::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    display: none !important;
  }

  /* Firefox - hide scrollbar for homepage and sidebar */
  .homepage-container,
  .sidebar-container {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  /* Hide main body scrollbar when on homepage */
  body:has(.homepage-container)::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    display: none !important;
  }

  body:has(.homepage-container) {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  /* Hide html scrollbar when on homepage */
  html:has(.homepage-container)::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    display: none !important;
  }

  html:has(.homepage-container) {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  *::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }

  *::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #333333 0%, #1a1a1a 100%);
    border-radius: 4px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #000000 0%, #333333 100%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transform: scale(1.05);
  }

  *::-webkit-scrollbar-thumb:active {
    background: #000000;
  }

  *::-webkit-scrollbar-corner {
    background: #f8f9fa;
  }

  /* Dark mode scrollbar */
  .dark *::-webkit-scrollbar-track {
    background: #1a1a1a;
    border: 1px solid #333333;
  }

  .dark *::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #666666 0%, #999999 100%);
    border: 1px solid #333333;
  }

  .dark *::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #999999 0%, #cccccc 100%);
    box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
  }

  .dark *::-webkit-scrollbar-thumb:active {
    background: #ffffff;
  }

  .dark *::-webkit-scrollbar-corner {
    background: #1a1a1a;
  }

  /* Firefox dark mode */
  .dark html {
    scrollbar-color: #666666 #1a1a1a;
  }

  /* Thin scrollbar variant for compact areas */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #666666 0%, #333333 100%);
    border-radius: 3px;
    border: none;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #333333 0%, #000000 100%);
  }

  /* Wide scrollbar variant for main content areas */
  .scrollbar-wide::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

  .scrollbar-wide::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #333333 0%, #1a1a1a 100%);
    border-radius: 6px;
    border: 2px solid #f8f9fa;
  }

  .scrollbar-wide::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #000000 0%, #333333 100%);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  /* Invisible scrollbar that appears on hover */
  .scrollbar-hover::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .scrollbar-hover:hover::-webkit-scrollbar {
    opacity: 1;
  }

  .scrollbar-hover::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #333333 0%, #1a1a1a 100%);
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  /* Professional legal document scrollbar */
  .scrollbar-legal::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  .scrollbar-legal::-webkit-scrollbar-track {
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 5px;
  }

  .scrollbar-legal::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
    border-radius: 5px;
    border: 1px solid #d1d5db;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .scrollbar-legal::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #111827 0%, #000000 100%);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Utility class to hide scrollbar completely */
  .scrollbar-hidden::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    display: none !important;
  }

  .scrollbar-hidden {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  .scrollbar-hidden *::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    display: none !important;
  }

  /* Smooth scrolling behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced scrollbar animations */
  *::-webkit-scrollbar-thumb {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  *::-webkit-scrollbar-thumb:hover {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Scrollbar track hover effects */
  *::-webkit-scrollbar-track:hover {
    background: #f1f3f4;
  }

  .dark *::-webkit-scrollbar-track:hover {
    background: #2d2d2d;
  }

  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  /* Professional Typography */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.2;
  }

  h1 {
    @apply text-3xl md:text-4xl lg:text-5xl;
    font-weight: 800;
    letter-spacing: -0.03em;
  }

  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl;
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  h3 {
    @apply text-xl md:text-2xl lg:text-3xl;
    font-weight: 600;
    letter-spacing: -0.02em;
  }

  /* Professional Legal Elements */
  .legal-heading {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-weight: 700;
    letter-spacing: -0.02em;
    color: hsl(var(--legal-navy));
  }

  .legal-text {
    line-height: 1.7;
    color: hsl(var(--legal-gray));
  }

  .legal-accent {
    color: hsl(var(--legal-gold));
  }

  /* Modern Font Utilities */
  .modern-heading {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-weight: 700;
    letter-spacing: -0.02em;
    line-height: 1.1;
  }

  .modern-title {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-weight: 600;
    letter-spacing: -0.015em;
    line-height: 1.3;
  }

  .modern-subtitle {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-weight: 500;
    letter-spacing: -0.01em;
    line-height: 1.4;
  }

  .modern-display {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-weight: 800;
    letter-spacing: -0.04em;
    line-height: 1.0;
  }

  /* High contrast mode */
  .high-contrast {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 90%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 90%;
    --muted-foreground: 0 0% 20%;
    --accent: 0 0% 90%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 100% 30%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 50%;
    --input: 0 0% 90%;
    --ring: 0 0% 0%;
  }

  .high-contrast.dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 80%;
    --accent: 0 0% 20%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 100% 70%;
    --destructive-foreground: 0 0% 0%;
    --border: 0 0% 50%;
    --input: 0 0% 20%;
    --ring: 0 0% 100%;
  }
}

.parallax {
  will-change: transform;
  transform-style: preserve-3d;
}

.parallax-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: translateZ(-10px) scale(2);
  z-index: -1;
}

.parallax-mid {
  transform: translateZ(-5px) scale(1.5);
}

.parallax-front {
  transform: translateZ(0) scale(1);
}

.section-marker {
  position: absolute;
  left: 0;
  width: 4px;
  background-color: #e6b000;
  transition: height 0.3s ease;
}

.hover-scale {
  @apply transition-transform duration-200 hover:scale-105;
}

.story-link {
  @apply relative inline-block after:content-[''] after:absolute after:w-full after:scale-x-0 after:h-0.5 after:bottom-0 after:left-0 after:bg-primary after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left;
}

/* Professional Animation Classes */
.professional-fade-in {
  animation: professionalFadeIn 0.6s ease-out forwards;
}

.professional-slide-up {
  animation: professionalSlideUp 0.7s ease-out forwards;
}

.professional-scale-in {
  animation: professionalScaleIn 0.5s ease-out forwards;
}

.professional-card-hover {
  @apply transition-all duration-300 ease-out;
}

.professional-card-hover:hover {
  @apply shadow-xl shadow-primary/10 -translate-y-1;
}

.professional-button {
  @apply transition-all duration-200 ease-out;
  @apply hover:shadow-lg hover:shadow-primary/20;
}

.professional-input {
  @apply transition-all duration-200 ease-out;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary;
}

/* Page Transition Classes */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s ease-in, transform 0.3s ease-in;
}

/* Professional Loading States */
.professional-skeleton {
  @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200;
  background-size: 200% 100%;
  animation: professionalSkeleton 1.5s ease-in-out infinite;
}

/* Professional Hover Effects */
.professional-hover-lift {
  @apply transition-transform duration-200 ease-out;
}

.professional-hover-lift:hover {
  @apply -translate-y-0.5;
}

.professional-hover-glow {
  @apply transition-shadow duration-300 ease-out;
}

.professional-hover-glow:hover {
  @apply shadow-lg shadow-primary/25;
}

/* Professional Keyframes */
@keyframes professionalFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes professionalSlideUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes professionalScaleIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes professionalSkeleton {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Line Clamp Utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

.line-clamp-5 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}

.line-clamp-6 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
}

/* Editor Styles */
.editor-block {
  position: relative;
}

.editor-block:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  pointer-events: none;
  position: absolute;
  left: 0;
  top: 0;
}

.editor-block:focus:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  pointer-events: none;
  position: absolute;
  left: 0;
  top: 0;
}

.editor-block[data-placeholder=""]:before {
  display: none;
}

/* Command Palette Styles */
.command-palette {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Spell Check Styles */
.editor-block[spellcheck="true"] {
  /* Enable browser spell checking */
}

/* Grammar Check Styles - Custom implementation would go here */
.grammar-error {
  border-bottom: 2px dotted #f59e0b;
  cursor: pointer;
}

.spell-error {
  border-bottom: 2px dotted #ef4444;
  cursor: pointer;
}

/* Drag and Drop Styles */
.drag-preview {
  opacity: 0.5;
  transform: rotate(5deg);
}

.drop-zone {
  border: 2px dashed #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
}

/* Fullscreen Editor Styles */
.fullscreen-editor {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
}

/* Editor Block Animations */
.editor-block-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.editor-block-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

.editor-block-exit {
  opacity: 1;
  transform: translateY(0);
}

.editor-block-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.2s ease-in, transform 0.2s ease-in;
}
